"""
OWASP TOP10漏洞检测模块
实现对OWASP TOP10漏洞的检测功能
"""
import re
import json
import socket
import requests
import urllib3
import warnings
import ssl
from urllib.parse import urljoin, urlparse, parse_qs
from utils.logger import logger
from concurrent.futures import ThreadPoolExecutor, as_completed

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class OwaspTop10Detector:
    """OWASP TOP10漏洞检测类"""

    def __init__(self):
        """初始化OWASP TOP10检测器"""
        self.owasp_rules = self._load_owasp_rules()

    def _load_owasp_rules(self):
        """加载OWASP Top 10漏洞检测规则"""
        try:
            # OWASP TOP10 2021规则
            owasp_rules = {
                # A01:2021 - 失效的访问控制
                'broken_access_control': {
                    'name': '失效的访问控制',
                    'patterns': [
                        r'admin',
                        r'dashboard',
                        r'manage',
                        r'api/users',
                        r'api/admin',
                        r'api/v1/admin'
                    ],
                    'severity': 'high',
                    'description': '失效的访问控制允许未授权用户访问敏感功能或数据',
                    'solution': '实施适当的访问控制机制，如基于角色的访问控制(RBAC)，并确保所有API端点都有正确的授权检查'
                },

                # A02:2021 - 加密机制失效
                'cryptographic_failures': {
                    'name': '加密机制失效',
                    'patterns': [],  # 通过特定检测方法实现
                    'severity': 'high',
                    'description': '加密机制失效可能导致敏感数据泄露',
                    'solution': '使用强加密算法，确保传输层安全(TLS)，避免使用过时的加密协议'
                },

                # A03:2021 - 注入
                'injection': {
                    'name': '注入攻击',
                    'patterns': [
                        # SQL注入
                        r'\' OR \'1\'=\'1',
                        r'\' OR 1=1',
                        r'\' OR SLEEP\((\d+)\); -- ',
                        r'\'; DROP TABLE users; -- ',
                        # NoSQL注入
                        r'{"$gt": ""}',
                        r'{"$ne": null}',
                        # 命令注入
                        r'; ls',
                        r'& dir',
                        r'| cat /etc/passwd'
                    ],
                    'severity': 'high',
                    'description': '注入攻击允许攻击者向解释器注入恶意代码',
                    'solution': '使用参数化查询，ORM，避免直接解释用户输入'
                },

                # A04:2021 - 不安全设计
                'insecure_design': {
                    'name': '不安全设计',
                    'patterns': [],  # 需要手动分析
                    'severity': 'medium',
                    'description': '不安全设计是指在软件设计阶段未考虑安全性',
                    'solution': '采用威胁建模，安全设计模式和原则'
                },

                # A05:2021 - 安全配置错误
                'security_misconfiguration': {
                    'name': '安全配置错误',
                    'patterns': [
                        r'phpinfo',
                        r'server-status',
                        r'server-info',
                        r'.git',
                        r'.env',
                        r'wp-config.php',
                        r'config.php'
                    ],
                    'severity': 'medium',
                    'description': '安全配置错误包括默认配置，开放云存储，错误处理暴露过多信息等',
                    'solution': '实施安全强化过程，最小化攻击面，移除不必要的功能'
                },

                # A06:2021 - 易受攻击和过时的组件
                'vulnerable_components': {
                    'name': '易受攻击和过时的组件',
                    'patterns': [],  # 通过版本检测实现
                    'severity': 'medium',
                    'description': '使用存在已知漏洞的组件或过时的组件',
                    'solution': '建立组件清单，定期更新，从官方源获取组件'
                },

                # A07:2021 - 身份认证与验证失败
                'authentication_failures': {
                    'name': '身份认证与验证失败',
                    'patterns': [
                        r'login',
                        r'signin',
                        r'register',
                        r'password',
                        r'reset'
                    ],
                    'severity': 'high',
                    'description': '身份认证与验证失败可能导致账户劫持',
                    'solution': '实施多因素认证，强密码策略，限制失败登录尝试'
                },

                # A08:2021 - 软件和数据完整性失败
                'integrity_failures': {
                    'name': '软件和数据完整性失败',
                    'patterns': [],  # 需要特定检测方法
                    'severity': 'medium',
                    'description': '软件和数据完整性失败包括使用未经验证的插件，库和模块',
                    'solution': '使用数字签名验证软件完整性，确保依赖项通过安全连接获取'
                },

                # A09:2021 - 安全日志记录和监控失效
                'logging_failures': {
                    'name': '安全日志记录和监控失效',
                    'patterns': [],  # 需要特定检测方法
                    'severity': 'medium',
                    'description': '缺乏足够的日志记录，检测，监控和响应',
                    'solution': '实施有效的监控和告警，确保日志记录足够详细'
                },

                # A10:2021 - 服务端请求伪造(SSRF)
                'ssrf': {
                    'name': '服务端请求伪造',
                    'patterns': [
                        r'url=http',
                        r'path=http',
                        r'redirect=http',
                        r'uri=http',
                        r'src=http'
                    ],
                    'severity': 'high',
                    'description': 'SSRF允许攻击者从服务器发起请求到内部资源',
                    'solution': '实施网络分段，白名单，禁止HTTP重定向，过滤和清理用户输入'
                },

                # 额外添加XSS检测
                'xss': {
                    'name': '跨站脚本攻击',
                    'patterns': [
                        r'<script>alert\("XSS"\)</script>',
                        r'<img src="javascript:alert(\'XSS\')" />',
                        r'<svg onload="alert(\'XSS\')" />',
                        r'javascript:alert(1)'
                    ],
                    'severity': 'high',
                    'description': 'XSS漏洞允许攻击者注入客户端脚本',
                    'solution': '对用户输入进行正确编码，使用内容安全策略(CSP)，使用安全的HTTP头部'
                }
            }
            return owasp_rules
        except Exception as e:
            logger.error(f"加载OWASP规则错误: {e}")
            return {}

    def _is_ip_address(self, address):
        """检查字符串是否为IP地址"""
        try:
            socket.inet_aton(address)
            return True
        except:
            return False

    def _safe_request(self, url, method='GET', params=None, data=None, headers=None, timeout=5):
        """安全地发送HTTP请求，处理异常"""
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", category=urllib3.exceptions.InsecureRequestWarning)
                if method.upper() == 'GET':
                    response = requests.get(
                        url,
                        params=params,
                        headers=headers,
                        verify=False,
                        timeout=timeout,
                        allow_redirects=True
                    )
                elif method.upper() == 'POST':
                    response = requests.post(
                        url,
                        params=params,
                        data=data,
                        headers=headers,
                        verify=False,
                        timeout=timeout,
                        allow_redirects=True
                    )
                return response
        except requests.exceptions.RequestException as e:
            logger.debug(f"请求 {url} 失败: {e}")
            return None

    def _detect_broken_access_control(self, target_url, vulnerabilities):
        """检测A01:失效的访问控制"""
        rule = self.owasp_rules.get('broken_access_control')
        if not rule:
            return

        # 尝试访问敏感路径
        for pattern in rule['patterns']:
            test_url = urljoin(target_url, pattern)
            response = self._safe_request(test_url)

            if response and response.status_code < 400:
                # 如果能够访问敏感路径，可能存在访问控制问题
                vulnerabilities.append({
                    'name': rule['name'],
                    'description': f"{rule['description']} - 能够访问敏感路径: {test_url}",
                    'severity': rule['severity'],
                    'affected_url': test_url,
                    'evidence': f"HTTP状态码: {response.status_code}",
                    'solution': rule['solution'],
                    'cve': 'OWASP-A01:2021',
                    'category': 'OWASP TOP10'
                })
                logger.info(f"发现失效的访问控制漏洞: {test_url}")

    def _detect_cryptographic_failures(self, target_url, vulnerabilities):
        """检测A02:加密机制失效"""
        rule = self.owasp_rules.get('cryptographic_failures')
        if not rule:
            return

        # 检查是否使用HTTPS
        if target_url.startswith('http://'):
            vulnerabilities.append({
                'name': rule['name'],
                'description': f"{rule['description']} - 未使用HTTPS加密传输",
                'severity': rule['severity'],
                'affected_url': target_url,
                'evidence': "使用HTTP而非HTTPS",
                'solution': rule['solution'],
                'cve': 'OWASP-A02:2021',
                'category': 'OWASP TOP10'
            })
            logger.info(f"发现加密机制失效漏洞: {target_url} 未使用HTTPS")

        # 如果是HTTPS，检查TLS版本和加密套件
        elif target_url.startswith('https://'):
            try:
                parsed_url = urlparse(target_url)
                hostname = parsed_url.hostname
                port = parsed_url.port or 443

                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE

                with socket.create_connection((hostname, port), timeout=5) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        version = ssock.version()
                        cipher = ssock.cipher()

                        # 检查TLS版本是否过低
                        if version in ['TLSv1', 'TLSv1.1', 'SSLv3', 'SSLv2']:
                            vulnerabilities.append({
                                'name': rule['name'],
                                'description': f"{rule['description']} - 使用过时的TLS版本: {version}",
                                'severity': rule['severity'],
                                'affected_url': target_url,
                                'evidence': f"TLS版本: {version}",
                                'solution': f"{rule['solution']} - 升级到TLSv1.2或更高版本",
                                'cve': 'OWASP-A02:2021',
                                'category': 'OWASP TOP10'
                            })
                            logger.info(f"发现加密机制失效漏洞: {target_url} 使用过时的TLS版本 {version}")
            except Exception as e:
                logger.debug(f"检查TLS版本失败: {e}")

    def _detect_injection(self, target_url, vulnerabilities):
        """检测A03:注入攻击"""
        # 检测SQL注入
        self._detect_sql_injection(target_url, vulnerabilities)

        # 检测命令注入
        self._detect_command_injection(target_url, vulnerabilities)

    def _detect_sql_injection(self, target_url, vulnerabilities):
        """检测SQL注入漏洞"""
        rule = self.owasp_rules.get('injection')
        if not rule:
            return

        # SQL注入测试参数和载荷
        test_params = ['id', 'user', 'search', 'query', 'page', 'category']
        sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1 -- ",
            "1' OR '1'='1",
            "admin' -- "
        ]

        # SQL错误模式
        error_patterns = [
            r'SQL syntax.*MySQL',
            r'Warning.*mysql_',
            r'valid MySQL result',
            r'MySqlClient\.',
            r'ORA-[0-9]+',
            r'Oracle error',
            r'SQL Server.*Driver',
            r'SQLite/JDBCDriver',
            r'SQLite\.Exception',
            r'System\.Data\.SQLite\.SQLiteException',
            r'Unclosed quotation mark after the character string',
            r'SQLITE_ERROR'
        ]

        # 尝试不同参数和载荷组合
        for param in test_params:
            for payload in sql_payloads:
                test_url = target_url
                response = self._safe_request(test_url, params={param: payload})

                if not response:
                    continue

                # 检查响应中是否包含SQL错误
                for pattern in error_patterns:
                    if re.search(pattern, response.text, re.IGNORECASE):
                        vulnerabilities.append({
                            'name': 'SQL注入',
                            'description': f"{rule['description']} - 检测到SQL注入漏洞",
                            'severity': rule['severity'],
                            'affected_url': f"{test_url}?{param}={payload}",
                            'evidence': f"SQL错误模式匹配: {pattern}",
                            'solution': rule['solution'],
                            'cve': 'OWASP-A03:2021',
                            'category': 'OWASP TOP10'
                        })
                        logger.info(f"发现SQL注入漏洞: {test_url}?{param}={payload}")
                        return  # 找到一个漏洞后停止测试

    def _detect_command_injection(self, target_url, vulnerabilities):
        """检测命令注入漏洞"""
        rule = self.owasp_rules.get('injection')
        if not rule:
            return

        # 命令注入测试参数和载荷
        test_params = ['cmd', 'exec', 'command', 'run', 'ping', 'query', 'ip']
        cmd_payloads = [
            '; ls -la',
            '& dir',
            '| cat /etc/passwd',
            '`id`',
            '$(whoami)'
        ]

        # 命令执行成功的模式
        success_patterns = [
            r'root:.*:0:0:',
            r'Directory of',
            r'total\s+\d+',
            r'uid=\d+\(',
            r'[a-zA-Z0-9_-]+\s+\d+\s+[a-zA-Z]+'
        ]

        # 尝试不同参数和载荷组合
        for param in test_params:
            for payload in cmd_payloads:
                test_url = target_url
                response = self._safe_request(test_url, params={param: payload})

                if not response:
                    continue

                # 检查响应中是否包含命令执行成功的模式
                for pattern in success_patterns:
                    if re.search(pattern, response.text, re.IGNORECASE):
                        vulnerabilities.append({
                            'name': '命令注入',
                            'description': f"{rule['description']} - 检测到命令注入漏洞",
                            'severity': rule['severity'],
                            'affected_url': f"{test_url}?{param}={payload}",
                            'evidence': f"命令执行模式匹配: {pattern}",
                            'solution': rule['solution'],
                            'cve': 'OWASP-A03:2021',
                            'category': 'OWASP TOP10'
                        })
                        logger.info(f"发现命令注入漏洞: {test_url}?{param}={payload}")
                        return  # 找到一个漏洞后停止测试

    def _detect_security_misconfiguration(self, target_url, vulnerabilities):
        """检测A05:安全配置错误"""
        rule = self.owasp_rules.get('security_misconfiguration')
        if not rule:
            return

        # 检查敏感文件和目录
        for pattern in rule['patterns']:
            test_url = urljoin(target_url, pattern)
            response = self._safe_request(test_url)

            if response and response.status_code < 400:
                vulnerabilities.append({
                    'name': rule['name'],
                    'description': f"{rule['description']} - 发现敏感文件或目录: {pattern}",
                    'severity': rule['severity'],
                    'affected_url': test_url,
                    'evidence': f"HTTP状态码: {response.status_code}",
                    'solution': rule['solution'],
                    'cve': 'OWASP-A05:2021',
                    'category': 'OWASP TOP10'
                })
                logger.info(f"发现安全配置错误漏洞: {test_url}")

        # 检查HTTP响应头
        response = self._safe_request(target_url)
        if response:
            # 检查缺少的安全头
            security_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
                'Content-Security-Policy': None,
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': None
            }

            missing_headers = []
            for header, value in security_headers.items():
                if header not in response.headers:
                    missing_headers.append(header)
                elif value and isinstance(value, list) and response.headers.get(header) not in value:
                    missing_headers.append(f"{header}={response.headers.get(header)}")
                elif value and not isinstance(value, list) and response.headers.get(header) != value:
                    missing_headers.append(f"{header}={response.headers.get(header)}")

            if missing_headers:
                vulnerabilities.append({
                    'name': rule['name'],
                    'description': f"{rule['description']} - 缺少安全HTTP头",
                    'severity': rule['severity'],
                    'affected_url': target_url,
                    'evidence': f"缺少的安全头: {', '.join(missing_headers)}",
                    'solution': f"{rule['solution']} - 添加适当的安全HTTP头",
                    'cve': 'OWASP-A05:2021',
                    'category': 'OWASP TOP10'
                })
                logger.info(f"发现安全配置错误漏洞: {target_url} 缺少安全HTTP头")

    def _detect_authentication_failures(self, target_url, vulnerabilities):
        """检测A07:身份认证与验证失败"""
        rule = self.owasp_rules.get('authentication_failures')
        if not rule:
            return

        # 检查登录页面
        for pattern in rule['patterns']:
            test_url = urljoin(target_url, pattern)
            response = self._safe_request(test_url)

            if response and response.status_code < 400:
                # 检查是否使用HTTPS
                if test_url.startswith('http://'):
                    vulnerabilities.append({
                        'name': rule['name'],
                        'description': f"{rule['description']} - 登录页面未使用HTTPS",
                        'severity': rule['severity'],
                        'affected_url': test_url,
                        'evidence': "登录页面使用HTTP而非HTTPS",
                        'solution': f"{rule['solution']} - 确保所有身份验证页面使用HTTPS",
                        'cve': 'OWASP-A07:2021',
                        'category': 'OWASP TOP10'
                    })
                    logger.info(f"发现身份认证与验证失败漏洞: {test_url} 未使用HTTPS")

    def _detect_ssrf(self, target_url, vulnerabilities):
        """检测A10:服务端请求伪造(SSRF)"""
        rule = self.owasp_rules.get('ssrf')
        if not rule:
            return

        # SSRF测试参数
        test_params = ['url', 'path', 'redirect', 'uri', 'src', 'dest', 'location']

        # SSRF测试载荷
        ssrf_payloads = [
            'http://localhost',
            'http://127.0.0.1',
            'http://***************',  # AWS元数据
            'http://[::1]',            # IPv6 localhost
            'file:///etc/passwd'
        ]

        # 尝试不同参数和载荷组合
        for param in test_params:
            for payload in ssrf_payloads:
                test_url = target_url
                response = self._safe_request(test_url, params={param: payload})

                if not response:
                    continue

                # 检查响应中是否包含敏感信息
                if 'root:' in response.text or 'localhost' in response.text:
                    vulnerabilities.append({
                        'name': rule['name'],
                        'description': f"{rule['description']} - 检测到SSRF漏洞",
                        'severity': rule['severity'],
                        'affected_url': f"{test_url}?{param}={payload}",
                        'evidence': "响应中包含敏感信息",
                        'solution': rule['solution'],
                        'cve': 'OWASP-A10:2021',
                        'category': 'OWASP TOP10'
                    })
                    logger.info(f"发现SSRF漏洞: {test_url}?{param}={payload}")
                    return  # 找到一个漏洞后停止测试

    def _detect_xss(self, target_url, vulnerabilities):
        """检测XSS漏洞"""
        rule = self.owasp_rules.get('xss')
        if not rule:
            return

        # XSS测试参数
        test_params = ['search', 'q', 'id', 'name', 'user', 'input', 'text', 'comment']

        # XSS测试载荷
        xss_payloads = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(\'XSS\')">',
            '<svg onload="alert(\'XSS\')">',
            '"><script>alert("XSS")</script>',
            '\'><script>alert("XSS")</script>'
        ]

        # 尝试不同参数和载荷组合
        for param in test_params:
            for payload in xss_payloads:
                test_url = target_url
                response = self._safe_request(test_url, params={param: payload})

                if not response:
                    continue

                # 检查响应中是否包含未经过滤的XSS载荷
                if payload in response.text:
                    vulnerabilities.append({
                        'name': rule['name'],
                        'description': f"{rule['description']} - 检测到XSS漏洞",
                        'severity': rule['severity'],
                        'affected_url': f"{test_url}?{param}={payload}",
                        'evidence': f"XSS载荷未被过滤: {payload}",
                        'solution': rule['solution'],
                        'cve': 'OWASP-A03:2021',
                        'category': 'OWASP TOP10'
                    })
                    logger.info(f"发现XSS漏洞: {test_url}?{param}={payload}")
                    return  # 找到一个漏洞后停止测试

    def detect_vulnerabilities(self, target_url, port=None, service_info=None):
        """
        检测目标URL的OWASP TOP10漏洞
        :param target_url: 目标URL或IP地址
        :param port: 端口号(可选)
        :param service_info: 服务信息(可选)
        :return: 漏洞列表
        """
        vulnerabilities = []

        # 如果输入是IP地址，转换为HTTP URL
        if self._is_ip_address(target_url):
            if port:
                target_url = f"http://{target_url}:{port}"
            else:
                target_url = f"http://{target_url}"

        # 确保URL格式正确
        if not target_url.startswith(('http://', 'https://')):
            target_url = f"http://{target_url}"

        logger.info(f"开始检测OWASP TOP10漏洞: {target_url}")

        try:
            # 检测A01:失效的访问控制
            self._detect_broken_access_control(target_url, vulnerabilities)

            # 检测A02:加密机制失效
            self._detect_cryptographic_failures(target_url, vulnerabilities)

            # 检测A03:注入攻击
            self._detect_injection(target_url, vulnerabilities)

            # 检测A05:安全配置错误
            self._detect_security_misconfiguration(target_url, vulnerabilities)

            # 检测A07:身份认证与验证失败
            self._detect_authentication_failures(target_url, vulnerabilities)

            # 检测A10:服务端请求伪造
            self._detect_ssrf(target_url, vulnerabilities)

            # 检测XSS
            self._detect_xss(target_url, vulnerabilities)

            logger.info(f"OWASP TOP10漏洞检测完成，发现 {len(vulnerabilities)} 个漏洞")
            return vulnerabilities

        except Exception as e:
            logger.error(f"OWASP TOP10漏洞检测错误: {e}")
            return vulnerabilities
