<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫描报告 - {{ job_id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        .report-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .report-header p {
            color: #7f8c8d;
            font-size: 14px;
        }
        .summary-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .summary-item {
            margin-bottom: 10px;
        }
        .summary-label {
            font-weight: bold;
            color: #7f8c8d;
        }
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .section-header {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin-bottom: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-alive {
            background-color: #2ecc71;
            color: white;
        }
        .status-open {
            background-color: #2ecc71;
            color: white;
        }
        .severity-high {
            background-color: #e74c3c;
            color: white;
        }
        .severity-medium {
            background-color: #f39c12;
            color: white;
        }
        .severity-low {
            background-color: #3498db;
            color: white;
        }
        .empty-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .mac-address {
            font-family: monospace;
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .os-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .os-windows {
            background-color: #0078d7;
            color: white;
        }
        .os-linux {
            background-color: #f8b312;
            color: black;
        }
        .os-macos {
            background-color: #999999;
            color: white;
        }
        .os-unknown {
            background-color: #7f8c8d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="report-header">
        <h1>网络安全扫描报告</h1>
        <p>扫描ID: {{ job_id }}</p>
        <p>生成时间: {{ results.scan_info.end_time }}</p>
    </div>

    <div class="summary-section">
        <h2>扫描概要</h2>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="summary-label">目标:</span>
                <span>{{ results.scan_info.target }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">开始时间:</span>
                <span>{{ results.scan_info.start_time }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">结束时间:</span>
                <span>{{ results.scan_info.end_time }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">状态:</span>
                <span>{{ results.scan_info.status }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">存活主机数:</span>
                <span>{{ results.alive_hosts|length }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">开放端口数:</span>
                <span>{{ results.open_ports|length }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">服务数:</span>
                <span>{{ results.services|length }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">漏洞数:</span>
                <span>{{ results.vulnerabilities|length }}</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2 class="section-header">存活主机</h2>
        {% if results.alive_hosts and results.alive_hosts|length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>IP地址</th>
                        <th>MAC地址</th>
                        <th>发现方式</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for host in results.alive_hosts %}
                        <tr>
                            <td>{{ host.ip }}</td>
                            <td>{% if host.mac %}<span class="mac-address">{{ host.mac }}</span>{% else %}未知{% endif %}</td>
                            <td>{{ host.discovery_method or 'ICMP' }}</td>
                            <td><span class="status-badge status-alive">存活</span></td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-message">未发现存活主机</div>
        {% endif %}
    </div>

    <div class="page-break"></div>

    <div class="section">
        <h2 class="section-header">操作系统信息</h2>
        {% if results.os_info and results.os_info|length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>主机</th>
                        <th>操作系统</th>
                        <th>版本</th>
                        <th>置信度</th>
                    </tr>
                </thead>
                <tbody>
                    {% for os in results.os_info %}
                        <tr>
                            <td>{{ os.ip }}</td>
                            <td>
                                {% if os.os and os.os != 'Unknown' %}
                                    <span class="os-badge os-{{ os.os|lower|replace(' ', '-') }}">{{ os.os }}</span>
                                {% else %}
                                    <span class="os-badge os-unknown">未知</span>
                                {% endif %}
                            </td>
                            <td>{{ os.details.osgen or '未知' }}</td>
                            <td>{{ os.confidence }}%</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-message">未能识别操作系统信息</div>
        {% endif %}
    </div>

    <div class="section">
        <h2 class="section-header">开放端口</h2>
        {% if results.open_ports and results.open_ports|length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>IP地址</th>
                        <th>端口</th>
                        <th>协议</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for port in results.open_ports %}
                        <tr>
                            <td>{{ port.ip or results.scan_info.target }}</td>
                            <td>{{ port.port }}</td>
                            <td>{{ port.protocol or 'tcp' }}</td>
                            <td><span class="status-badge status-open">{{ port.state or '开放' }}</span></td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-message">未发现开放端口</div>
        {% endif %}
    </div>

    <div class="page-break"></div>

    <div class="section">
        <h2 class="section-header">服务</h2>
        {% if results.services and results.services|length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>IP地址</th>
                        <th>服务</th>
                        <th>版本</th>
                        <th>端口</th>
                        <th>协议</th>
                    </tr>
                </thead>
                <tbody>
                    {% set unique_services = {} %}
                    {% for service in results.services %}
                        {% set service_key = service.host_ip ~ '_' ~ service.name ~ '_' ~ service.port ~ '_' ~ service.protocol %}
                        {% if service_key not in unique_services %}
                            {% set _ = unique_services.update({service_key: service}) %}
                            <tr>
                                <td>{{ service.host_ip or service.ip }}</td>
                                <td>{{ service.name or service.service }}</td>
                                <td>{{ service.version or '未知' }}</td>
                                <td>{{ service.port }}</td>
                                <td>{{ service.protocol or 'tcp' }}</td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-message">未识别到任何服务</div>
        {% endif %}
    </div>

    <div class="section">
        <h2 class="section-header">漏洞检测结果</h2>
        {% if results.vulnerabilities and results.vulnerabilities|length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>漏洞名称</th>
                        <th>严重程度</th>
                        <th>影响URL</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vulnerability in results.vulnerabilities %}
                        <tr>
                            <td>{{ vulnerability.name }}</td>
                            <td>
                                <span class="severity-badge severity-{{ vulnerability.severity }}">
                                    {{ vulnerability.severity or '未知' }}
                                </span>
                            </td>
                            <td>{{ vulnerability.affected_url or results.scan_info.target }}</td>
                            <td>{{ vulnerability.description or '无描述' }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-message">未发现任何漏洞</div>
        {% endif %}
    </div>

    <div class="footer">
        <p>本报告由网络安全扫描系统自动生成 &copy; {{ results.scan_info.end_time[:4] }}</p>
    </div>
</body>
</html>
