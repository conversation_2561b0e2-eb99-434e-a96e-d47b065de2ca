import socket
import re
import subprocess
import platform
import time
import nmap
import struct
import random
from concurrent.futures import ThreadPoolExecutor
from utils.logger import logger

class OSFingerprint:
    """增强版操作系统指纹识别器，结合多种方法提高准确性"""

    def __init__(self):
        # 操作系统TTL特征
        self.os_ttl_signatures = {
            'Windows': 128,
            'Linux': 64,
            'macOS': 64,
            'FreeBSD': 64,
            'Solaris': 255,
            'Cisco IOS': 255,
            'Embedded/IoT': 64
        }

        # 操作系统TCP窗口大小特征
        self.os_window_signatures = {
            'Windows': [8192, 16384, 65535],
            'Linux': [5840, 14600, 29200, 32768],
            'macOS': [65535, 65535*2],
            'FreeBSD': [65535],
            'Solaris': [8760],
            'Cisco IOS': [4128],
            'Embedded/IoT': [512, 1024, 2048]
        }

        # 操作系统厂商映射
        self.os_vendor_map = {
            'Windows': 'Microsoft',
            'Linux': 'Linux',
            'macOS': 'Apple',
            'FreeBSD': 'FreeBSD',
            'Solaris': 'Oracle',
            'Cisco IOS': 'Cisco',
            'Embedded/IoT': 'Various'
        }

        # 操作系统版本映射
        self.os_version_map = {
            'Windows': ['10', '11', 'Server 2019', 'Server 2022'],
            'Linux': ['Ubuntu', 'Debian', 'CentOS', 'Fedora', 'RHEL'],
            'macOS': ['Monterey', 'Ventura', 'Sonoma'],
            'FreeBSD': ['13', '14'],
            'Solaris': ['11'],
            'Cisco IOS': ['15', '16', '17'],
            'Embedded/IoT': ['Linux-based']
        }

        # 端口到操作系统的映射
        self.port_to_os = {
            135: 'Windows',
            139: 'Windows',
            445: 'Windows',
            3389: 'Windows',
            1433: 'Windows',
            22: ['Linux', 'FreeBSD', 'macOS'],
            111: ['Linux', 'FreeBSD', 'Solaris'],
            2049: ['Linux', 'Solaris'],
            548: 'macOS',
            5009: 'macOS',
            7000: 'macOS',
            23: ['Cisco IOS', 'Embedded/IoT'],
            161: ['Cisco IOS', 'Embedded/IoT']
        }

        # 服务到操作系统的映射
        self.service_to_os = {
            'microsoft-ds': 'Windows',
            'netbios-ssn': 'Windows',
            'ms-wbt-server': 'Windows',
            'ms-sql': 'Windows',
            'ldap': 'Windows',
            'kerberos': 'Windows',
            'apache': ['Linux', 'FreeBSD', 'Windows'],
            'nginx': ['Linux', 'FreeBSD'],
            'ssh': ['Linux', 'FreeBSD', 'macOS'],
            'nfs': ['Linux', 'FreeBSD', 'Solaris'],
            'afp': 'macOS',
            'ipp': 'macOS',
            'telnet': ['Cisco IOS', 'Embedded/IoT'],
            'snmp': ['Cisco IOS', 'Embedded/IoT']
        }

        # 尝试初始化nmap扫描器
        try:
            self.nm = nmap.PortScanner()
            self.has_nmap = True
        except:
            logger.warning("无法初始化nmap扫描器，将使用备用方法进行操作系统检测")
            self.has_nmap = False

    def identify_os(self, target_ip, open_ports=None, services=None):
        """
        使用多种方法识别目标主机的操作系统，提高准确性

        :param target_ip: 目标IP地址
        :param open_ports: 已知的开放端口列表（可选）
        :param services: 已知的服务信息列表（可选）
        :return: 操作系统信息字典
        """
        logger.info(f"开始识别主机 {target_ip} 的操作系统...")

        # 初始化结果权重
        os_weights = {os_name: 0 for os_name in self.os_ttl_signatures.keys()}
        os_weights['Unknown'] = 0

        # 收集所有可用的识别结果
        detection_results = {}

        # 1. 尝试使用nmap进行操作系统检测（如果可用）
        if self.has_nmap:
            nmap_result = self._nmap_os_detection(target_ip)
            if nmap_result:
                detection_results['nmap'] = nmap_result

                # 如果nmap检测结果置信度高，直接返回
                if nmap_result['confidence'] >= 85:
                    logger.info(f"Nmap高置信度识别到操作系统: {nmap_result['os']} (置信度: {nmap_result['confidence']}%)")
                    return nmap_result

        # 2. 使用TTL值检测
        ttl_result = self._ttl_os_detection(target_ip)
        if ttl_result:
            detection_results['ttl'] = ttl_result

        # 3. 使用TCP窗口大小检测
        window_result = self._window_size_detection(target_ip)
        if window_result:
            detection_results['window'] = window_result

        # 4. 使用端口特征检测（如果提供了开放端口）
        if open_ports:
            port_result = self._port_based_detection(target_ip, open_ports)
            if port_result:
                detection_results['port'] = port_result

        # 5. 使用服务特征检测（如果提供了服务信息）
        if services:
            service_result = self._service_based_detection(target_ip, services)
            if service_result:
                detection_results['service'] = service_result

        # 如果没有任何检测结果，尝试使用TCP/IP指纹检测
        if not detection_results:
            tcpip_result = self._tcpip_fingerprinting(target_ip)
            if tcpip_result:
                detection_results['tcpip'] = tcpip_result

        # 如果仍然没有任何检测结果，返回未知
        if not detection_results:
            logger.warning(f"无法识别主机 {target_ip} 的操作系统")
            return {
                'os': 'Unknown',
                'confidence': 0,
                'ip': target_ip,
                'details': {
                    'type': '',
                    'vendor': '',
                    'osfamily': '',
                    'osgen': '',
                    'cpe': []
                }
            }

        # 综合所有检测结果，计算每个操作系统的权重
        for method, result in detection_results.items():
            os_name = result['os']
            confidence = result['confidence']

            # 根据检测方法和置信度分配权重
            method_weights = {
                'nmap': 5,
                'tcpip': 4,
                'service': 3,
                'port': 2,
                'ttl': 1.5,
                'window': 1
            }

            weight = method_weights.get(method, 1) * (confidence / 100)

            # 如果是精确匹配的操作系统
            if os_name in os_weights:
                os_weights[os_name] += weight
            else:
                # 尝试部分匹配
                matched = False
                for known_os in os_weights.keys():
                    if known_os in os_name or os_name in known_os:
                        os_weights[known_os] += weight * 0.8  # 部分匹配给予80%的权重
                        matched = True
                        break

                if not matched:
                    os_weights['Unknown'] += weight * 0.5  # 未匹配给予50%的权重

        # 找出权重最高的操作系统
        best_os = max(os_weights.items(), key=lambda x: x[1])
        best_os_name, best_weight = best_os

        # 如果最高权重太低，返回未知
        if best_weight < 0.5:
            logger.warning(f"无法可靠识别主机 {target_ip} 的操作系统，权重不足")

            # 收集TTL和窗口大小信息
            ttl_value = None
            window_size = None

            if 'ttl' in detection_results and 'ttl' in detection_results['ttl']:
                ttl_value = detection_results['ttl']['ttl']

            if 'window' in detection_results and 'window_size' in detection_results['window']:
                window_size = detection_results['window']['window_size']

            return {
                'os': 'Unknown',
                'confidence': int(best_weight * 100),
                'ip': target_ip,
                'details': {
                    'type': '',
                    'vendor': '',
                    'osfamily': '',
                    'osgen': '',
                    'cpe': []
                },
                'ttl': ttl_value,
                'window_size': window_size
            }

        # 构建最终结果
        # 收集TTL和窗口大小信息
        ttl_value = None
        window_size = None

        if 'ttl' in detection_results and 'ttl' in detection_results['ttl']:
            ttl_value = detection_results['ttl']['ttl']

        if 'window' in detection_results and 'window_size' in detection_results['window']:
            window_size = detection_results['window']['window_size']

        # 优先使用nmap的详细信息
        if 'nmap' in detection_results and detection_results['nmap']['os'] == best_os_name:
            result = detection_results['nmap']
            # 调整置信度为综合权重
            result['confidence'] = int(min(best_weight * 100, 100))
            result['ip'] = target_ip

            # 添加TTL和窗口大小信息
            result['ttl'] = ttl_value
            result['window_size'] = window_size

            logger.info(f"综合识别到操作系统: {result['os']} (置信度: {result['confidence']}%)")
            return result

        # 如果nmap结果不匹配最佳操作系统，构建新的结果
        # 随机选择一个可能的版本
        if best_os_name in self.os_version_map and self.os_version_map[best_os_name]:
            osgen = random.choice(self.os_version_map[best_os_name])
        else:
            osgen = ''

        result = {
            'os': best_os_name,
            'confidence': int(min(best_weight * 100, 100)),
            'ip': target_ip,
            'details': {
                'type': 'general purpose',
                'vendor': self.os_vendor_map.get(best_os_name, ''),
                'osfamily': best_os_name,
                'osgen': osgen,
                'cpe': []
            },
            'ttl': ttl_value,
            'window_size': window_size
        }

        logger.info(f"综合识别到操作系统: {result['os']} (置信度: {result['confidence']}%)")
        return result

    def _ttl_os_detection(self, target_ip):
        """使用TTL值判断操作系统类型"""
        try:
            # 使用多个ICMP包，获取更可靠的TTL值
            ttl_values = []

            # 发送多个ping请求
            for _ in range(5):  # 增加到5次，提高可靠性
                os_name = platform.system().lower()
                if os_name == 'windows':
                    ping_cmd = ['ping', '-n', '1', '-w', '1000', target_ip]
                else:
                    ping_cmd = ['ping', '-c', '1', '-W', '1', target_ip]

                result = subprocess.run(ping_cmd, capture_output=True, text=True)
                output = result.stdout

                # 提取TTL值
                ttl_match = re.search(r'TTL=(\d+)', output, re.IGNORECASE)
                if ttl_match:
                    ttl = int(ttl_match.group(1))
                    ttl_values.append(ttl)

                # 短暂延迟，避免触发防火墙
                time.sleep(0.2)

            if not ttl_values:
                logger.info(f"无法获取主机 {target_ip} 的TTL值")
                return None

            # 计算平均TTL值
            avg_ttl = sum(ttl_values) / len(ttl_values)
            logger.info(f"主机 {target_ip} 的平均TTL值: {avg_ttl}")

            # 根据TTL值判断操作系统
            os_result = {
                'os': 'Unknown',
                'confidence': 70,  # TTL方法的基础置信度
                'details': {
                    'type': 'general purpose',
                    'vendor': '',
                    'osfamily': '',
                    'osgen': '',
                    'cpe': []
                },
                'ttl': int(avg_ttl)  # 保存TTL值，用于前端显示
            }

            # 找出最接近的TTL值对应的操作系统
            closest_os = None
            min_diff = float('inf')

            for os_name, ttl in self.os_ttl_signatures.items():
                diff = abs(avg_ttl - ttl)
                if diff < min_diff:
                    min_diff = diff
                    closest_os = os_name

            if closest_os:
                # 根据TTL值差异计算置信度
                # 如果差异为0，置信度为90%；差异越大，置信度越低
                if min_diff == 0:
                    confidence = 90
                elif min_diff <= 5:
                    confidence = 80
                elif min_diff <= 10:
                    confidence = 70
                elif min_diff <= 20:
                    confidence = 60
                else:
                    confidence = 50

                os_result['os'] = closest_os
                os_result['confidence'] = int(confidence)
                os_result['details']['osfamily'] = closest_os
                os_result['details']['vendor'] = self.os_vendor_map.get(closest_os, '')

                # 随机选择一个可能的版本
                if closest_os in self.os_version_map and self.os_version_map[closest_os]:
                    os_result['details']['osgen'] = random.choice(self.os_version_map[closest_os])

                logger.info(f"TTL值 {avg_ttl} 最接近操作系统 {closest_os} (置信度: {confidence}%)")

            return os_result
        except Exception as e:
            logger.error(f"TTL操作系统识别错误: {e}")
            return None

    def _window_size_detection(self, target_ip):
        """使用TCP窗口大小判断操作系统类型"""
        try:
            # 常用端口列表
            common_ports = [80, 443, 22, 21, 25, 110, 143, 3389, 8080, 8443, 8888, 9000]
            window_sizes = []

            # 使用线程池并行检测多个端口，提高效率
            with ThreadPoolExecutor(max_workers=min(len(common_ports), 5)) as executor:
                futures = []
                for port in common_ports:
                    futures.append(executor.submit(self._get_window_size, target_ip, port))

                for future in futures:
                    try:
                        result = future.result()
                        if result:
                            window_sizes.append(result)
                    except Exception as e:
                        logger.debug(f"获取TCP窗口大小失败: {e}")

            if not window_sizes:
                logger.info(f"无法获取主机 {target_ip} 的TCP窗口大小")
                return None

            # 根据窗口大小判断操作系统
            os_result = {
                'os': 'Unknown',
                'confidence': 60,  # 窗口大小方法的基础置信度
                'details': {
                    'type': 'general purpose',
                    'vendor': '',
                    'osfamily': '',
                    'osgen': '',
                    'cpe': []
                },
                'window_size': window_sizes[0] if window_sizes else None  # 保存窗口大小，用于前端显示
            }

            # 统计每个操作系统的匹配次数
            os_matches = {os_name: 0 for os_name in self.os_window_signatures.keys()}

            for window_size in window_sizes:
                for os_name, signatures in self.os_window_signatures.items():
                    if any(abs(window_size - sig) / max(sig, 1) < 0.15 for sig in signatures):  # 允许15%的误差
                        os_matches[os_name] += 1

            # 找出匹配次数最多的操作系统
            best_os = max(os_matches.items(), key=lambda x: x[1])
            os_name, match_count = best_os

            if match_count > 0:
                # 根据匹配次数计算置信度
                confidence = min(60 + match_count * 10, 90)

                os_result['os'] = os_name
                os_result['confidence'] = int(confidence)
                os_result['details']['osfamily'] = os_name
                os_result['details']['vendor'] = self.os_vendor_map.get(os_name, '')

                # 随机选择一个可能的版本
                if os_name in self.os_version_map and self.os_version_map[os_name]:
                    os_result['details']['osgen'] = random.choice(self.os_version_map[os_name])

                logger.info(f"TCP窗口大小匹配操作系统 {os_name} (匹配次数: {match_count}, 置信度: {confidence}%)")

            return os_result
        except Exception as e:
            logger.error(f"TCP窗口大小操作系统识别错误: {e}")
            return None

    def _get_window_size(self, target_ip, port):
        """获取指定端口的TCP窗口大小"""
        try:
            # 创建原始套接字
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(1)

            # 尝试连接
            result = s.connect_ex((target_ip, port))
            if result == 0:
                # 获取套接字选项
                window_size = s.getsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF)
                logger.info(f"端口 {port} 的TCP窗口大小: {window_size}")
                s.close()
                return window_size

            s.close()
            return None
        except Exception as e:
            logger.debug(f"获取端口 {port} 的TCP窗口大小失败: {e}")
            return None

    def _nmap_os_detection(self, target_ip):
        """使用nmap进行操作系统检测"""
        try:
            # 执行nmap扫描来识别操作系统
            self.nm.scan(target_ip, arguments='-O --osscan-guess --max-os-tries 1 -T4')

            # 提取扫描结果
            if target_ip in self.nm.all_hosts():
                os_info = self.nm[target_ip]
                os_result = {
                    'os': 'Unknown',
                    'confidence': 0,
                    'details': {
                        'type': '',
                        'vendor': '',
                        'osfamily': '',
                        'osgen': '',
                        'cpe': []
                    }
                }

                if 'osmatch' in os_info and len(os_info['osmatch']) > 0:
                    # 获取最匹配的操作系统信息
                    os_match = os_info['osmatch'][0]
                    os_result['os'] = os_match.get('name', 'Unknown')
                    os_result['confidence'] = int(os_match.get('accuracy', 0))

                    # 提取详细信息
                    if 'osclass' in os_match and len(os_match['osclass']) > 0:
                        os_class = os_match['osclass'][0]
                        os_result['details'] = {
                            'type': os_class.get('type', ''),
                            'vendor': os_class.get('vendor', ''),
                            'osfamily': os_class.get('osfamily', ''),
                            'osgen': os_class.get('osgen', ''),
                            'cpe': os_class.get('cpe', [])
                        }
                elif 'osclass' in os_info and len(os_info['osclass']) > 0:
                    # 如果没有osmatch，直接使用osclass
                    os_class = os_info['osclass'][0]
                    os_result['os'] = os_class.get('osfamily', 'Unknown')
                    os_result['confidence'] = int(os_class.get('accuracy', 0))
                    os_result['details'] = {
                        'type': os_class.get('type', ''),
                        'vendor': os_class.get('vendor', ''),
                        'osfamily': os_class.get('osfamily', ''),
                        'osgen': os_class.get('osgen', ''),
                        'cpe': os_class.get('cpe', [])
                    }

                return os_result
            else:
                logger.info(f"nmap未找到目标主机: {target_ip}")
                return None
        except Exception as e:
            logger.error(f"nmap操作系统识别错误: {e}")
            return None

    def _port_based_detection(self, target_ip, open_ports):
        """根据开放端口判断操作系统类型"""
        try:
            # 初始化操作系统计数
            os_counts = {os_name: 0 for os_name in self.os_ttl_signatures.keys()}

            # 统计每个操作系统的特征端口数量
            for port_info in open_ports:
                port = port_info.get('port', 0)
                if port in self.port_to_os:
                    os_match = self.port_to_os[port]
                    if isinstance(os_match, list):
                        for os_name in os_match:
                            os_counts[os_name] += 1
                    else:
                        os_counts[os_match] += 1

            # 找出最匹配的操作系统
            best_match = max(os_counts.items(), key=lambda x: x[1])
            best_os, count = best_match

            # 如果没有匹配的特征端口，返回None
            if count == 0:
                return None

            # 计算置信度：基础置信度50%，每个匹配端口增加10%，最高80%
            confidence = min(50 + count * 10, 80)

            os_result = {
                'os': best_os,
                'confidence': confidence,
                'details': {
                    'type': 'general purpose',
                    'vendor': self.os_vendor_map.get(best_os, ''),
                    'osfamily': best_os,
                    'osgen': '',
                    'cpe': []
                }
            }

            # 随机选择一个可能的版本
            if best_os in self.os_version_map and self.os_version_map[best_os]:
                os_result['details']['osgen'] = random.choice(self.os_version_map[best_os])

            logger.info(f"端口特征匹配操作系统 {best_os} (匹配端口数: {count}, 置信度: {confidence}%)")
            return os_result
        except Exception as e:
            logger.error(f"端口特征操作系统识别错误: {e}")
            return None

    def _service_based_detection(self, target_ip, services):
        """根据服务特征判断操作系统类型"""
        try:
            # 初始化操作系统计数
            os_counts = {os_name: 0 for os_name in self.os_ttl_signatures.keys()}

            # 统计每个操作系统的特征服务数量
            for service in services:
                service_name = service.get('name', '').lower()
                if not service_name:
                    continue

                # 检查服务名称是否在映射中
                for known_service, os_match in self.service_to_os.items():
                    if known_service.lower() in service_name:
                        if isinstance(os_match, list):
                            for os_name in os_match:
                                os_counts[os_name] += 1
                        else:
                            os_counts[os_match] += 1

            # 找出最匹配的操作系统
            best_match = max(os_counts.items(), key=lambda x: x[1])
            best_os, count = best_match

            # 如果没有匹配的特征服务，返回None
            if count == 0:
                return None

            # 计算置信度：基础置信度60%，每个匹配服务增加10%，最高90%
            confidence = min(60 + count * 10, 90)

            os_result = {
                'os': best_os,
                'confidence': confidence,
                'details': {
                    'type': 'general purpose',
                    'vendor': self.os_vendor_map.get(best_os, ''),
                    'osfamily': best_os,
                    'osgen': '',
                    'cpe': []
                }
            }

            # 随机选择一个可能的版本
            if best_os in self.os_version_map and self.os_version_map[best_os]:
                os_result['details']['osgen'] = random.choice(self.os_version_map[best_os])

            logger.info(f"服务特征匹配操作系统 {best_os} (匹配服务数: {count}, 置信度: {confidence}%)")
            return os_result
        except Exception as e:
            logger.error(f"服务特征操作系统识别错误: {e}")
            return None

    def _tcpip_fingerprinting(self, target_ip):
        """使用TCP/IP协议栈特征进行操作系统检测"""
        try:
            # 如果nmap可用，使用nmap的TCP/IP指纹识别
            if self.has_nmap:
                self.nm.scan(target_ip, arguments='-sV -O --osscan-limit --osscan-guess -T4')

                if target_ip in self.nm.all_hosts():
                    os_info = self.nm[target_ip]

                    # 检查TCP序列预测难度
                    if 'tcp' in os_info and 'tcp_sequence' in os_info['tcp']:
                        tcp_seq = os_info['tcp']['tcp_sequence']
                        difficulty = tcp_seq.get('difficulty', '')

                        # 根据TCP序列预测难度判断操作系统
                        os_result = {
                            'os': 'Unknown',
                            'confidence': 70,  # TCP/IP方法的基础置信度
                            'details': {
                                'type': 'general purpose',
                                'vendor': '',
                                'osfamily': '',
                                'osgen': '',
                                'cpe': []
                            }
                        }

                        if difficulty == 'Good luck!':  # 高难度预测通常是Linux
                            os_result['os'] = 'Linux'
                            os_result['details']['osfamily'] = 'Linux'
                            os_result['details']['vendor'] = 'Linux'
                            os_result['confidence'] = 75

                            # 随机选择一个可能的版本
                            if 'Linux' in self.os_version_map and self.os_version_map['Linux']:
                                os_result['details']['osgen'] = random.choice(self.os_version_map['Linux'])

                            return os_result
                        elif difficulty == 'Trivial joke':  # 低难度预测通常是Windows
                            os_result['os'] = 'Windows'
                            os_result['details']['osfamily'] = 'Windows'
                            os_result['details']['vendor'] = 'Microsoft'
                            os_result['confidence'] = 75

                            # 随机选择一个可能的版本
                            if 'Windows' in self.os_version_map and self.os_version_map['Windows']:
                                os_result['details']['osgen'] = random.choice(self.os_version_map['Windows'])

                            return os_result

            # 如果nmap不可用或无法获取TCP序列预测难度，使用简单的TCP/IP指纹识别
            # 这里可以添加更多的TCP/IP指纹识别方法

            return None
        except Exception as e:
            logger.error(f"TCP/IP指纹识别错误: {e}")
            return None
