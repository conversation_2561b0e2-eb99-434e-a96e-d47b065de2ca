<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫描报告 - {{ job_id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        .report-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .report-header p {
            color: #7f8c8d;
            font-size: 14px;
        }
        .summary-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .summary-item {
            margin-bottom: 10px;
        }
        .summary-label {
            font-weight: bold;
            color: #7f8c8d;
            display: block;
            margin-bottom: 5px;
        }
        .summary-value {
            font-size: 16px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section-header {
            background: linear-gradient(90deg, #3498db, #2980b9);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-header h2 {
            margin: 0;
            font-size: 18px;
        }
        .count-badge {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid #ddd;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-alive {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        .status-open {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        .severity-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        .severity-high {
            background-color: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }
        .severity-medium {
            background-color: rgba(243, 156, 18, 0.2);
            color: #f39c12;
        }
        .severity-low {
            background-color: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }
        .empty-message {
            text-align: center;
            padding: 30px;
            color: #7f8c8d;
            font-style: italic;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px dashed #ddd;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            font-size: 14px;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .mac-address {
            font-family: monospace;
            background-color: rgba(52, 152, 219, 0.1);
            padding: 3px 6px;
            border-radius: 4px;
            color: #2980b9;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .os-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            color: #fff;
        }
        .os-windows {
            background-color: #0078d7;
        }
        .os-linux {
            background-color: #f8b312;
            color: #333;
        }
        .os-macos {
            background-color: #999999;
        }
        .os-unknown {
            background-color: #9e9e9e;
        }
        .confidence-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff9800 0%, #4caf50 100%);
            border-radius: 10px;
        }
        .confidence-text {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 12px;
            font-weight: 500;
        }
        .vuln-chart {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .vuln-count {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border-radius: 8px;
            min-width: 100px;
        }
        .vuln-count.high {
            background-color: rgba(231, 76, 60, 0.1);
        }
        .vuln-count.medium {
            background-color: rgba(243, 156, 18, 0.1);
        }
        .vuln-count.low {
            background-color: rgba(46, 204, 113, 0.1);
        }
        .vuln-count .count {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .vuln-count.high .count {
            color: #e74c3c;
        }
        .vuln-count.medium .count {
            color: #f39c12;
        }
        .vuln-count.low .count {
            color: #2ecc71;
        }
        .vuln-count .label {
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-header">
            <h1>网络安全扫描报告</h1>
            <p>扫描ID: {{ job_id }}</p>
            <p>生成时间: {{ results.scan_info.end_time }}</p>
        </div>

        <div class="summary-section">
            <h2>扫描概要</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">目标:</span>
                    <span class="summary-value">{{ results.scan_info.target }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">开始时间:</span>
                    <span class="summary-value">{{ results.scan_info.start_time }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">结束时间:</span>
                    <span class="summary-value">{{ results.scan_info.end_time }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">状态:</span>
                    <span class="summary-value">{{ results.scan_info.status }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">存活主机数:</span>
                    <span class="summary-value">{{ results.alive_hosts|length }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">开放端口数:</span>
                    <span class="summary-value">{{ results.open_ports|length }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">服务数:</span>
                    <span class="summary-value">{{ results.services|length }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">漏洞数:</span>
                    <span class="summary-value">{{ results.vulnerabilities|length }}</span>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">
                <h2>存活主机</h2>
                <span class="count-badge">{{ results.alive_hosts|length }}</span>
            </div>
            {% if results.alive_hosts and results.alive_hosts|length > 0 %}
                <table>
                    <thead>
                        <tr>
                            <th>IP地址</th>
                            <th>MAC地址</th>
                            <th>发现方式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for host in results.alive_hosts %}
                            <tr>
                                <td>{{ host.ip }}</td>
                                <td>
                                    {% if host.mac %}
                                        <span class="mac-address">{{ host.mac }}</span>
                                    {% else %}
                                        <span style="color: #999; font-style: italic;">未知</span>
                                    {% endif %}
                                </td>
                                <td>{{ host.discovery_method or 'ICMP' }}</td>
                                <td><span class="status-badge status-alive">存活</span></td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-message">未发现存活主机</div>
            {% endif %}
        </div>

        <div class="section">
            <div class="section-header">
                <h2>操作系统信息</h2>
                <span class="count-badge">{{ results.os_info|length }}</span>
            </div>
            {% if results.os_info and results.os_info|length > 0 %}
                <table>
                    <thead>
                        <tr>
                            <th>主机</th>
                            <th>操作系统</th>
                            <th>版本</th>
                            <th>置信度</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for os in results.os_info %}
                            <tr>
                                <td>{{ os.ip }}</td>
                                <td>
                                    {% if os.os and os.os != 'Unknown' %}
                                        <span class="os-badge os-{{ os.os|lower|replace(' ', '-') }}">
                                            {{ os.os }}
                                        </span>
                                    {% else %}
                                        <span class="os-badge os-unknown">未知</span>
                                    {% endif %}
                                </td>
                                <td>{{ os.details.osgen or '未知' }}</td>
                                <td>
                                    {% if os.confidence and os.confidence > 0 %}
                                        <div class="confidence-bar">
                                            <div class="confidence-fill" style="width: {{ os.confidence }}%;"></div>
                                            <span class="confidence-text">{{ os.confidence }}%</span>
                                        </div>
                                    {% else %}
                                        <span style="color: #999; font-style: italic;">未知</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-message">未能识别操作系统信息</div>
            {% endif %}
        </div>

        <div class="section">
            <div class="section-header">
                <h2>开放端口</h2>
                <span class="count-badge">{{ results.open_ports|length }}</span>
            </div>
            {% if results.open_ports and results.open_ports|length > 0 %}
                <table>
                    <thead>
                        <tr>
                            <th>IP地址</th>
                            <th>端口</th>
                            <th>协议</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for port in results.open_ports %}
                            <tr>
                                <td>{{ port.ip or results.scan_info.target }}</td>
                                <td>{{ port.port }}</td>
                                <td>{{ port.protocol or 'tcp' }}</td>
                                <td><span class="status-badge status-open">{{ port.state or '开放' }}</span></td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-message">未发现开放端口</div>
            {% endif %}
        </div>

        <div class="section">
            <div class="section-header">
                <h2>服务</h2>
                {% set unique_services = {} %}
                {% for service in results.services %}
                    {% set service_key = service.host_ip ~ '_' ~ service.name ~ '_' ~ service.port ~ '_' ~ service.protocol %}
                    {% if service_key not in unique_services %}
                        {% set _ = unique_services.update({service_key: 1}) %}
                    {% endif %}
                {% endfor %}
                <span class="count-badge">{{ unique_services|length }}</span>
            </div>
            {% if results.services and results.services|length > 0 %}
                <table>
                    <thead>
                        <tr>
                            <th>IP地址</th>
                            <th>服务</th>
                            <th>版本</th>
                            <th>端口</th>
                            <th>协议</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set unique_services = {} %}
                        {% for service in results.services %}
                            {% set service_key = service.host_ip ~ '_' ~ service.name ~ '_' ~ service.port ~ '_' ~ service.protocol %}
                            {% if service_key not in unique_services %}
                                {% set _ = unique_services.update({service_key: service}) %}
                                <tr>
                                    <td>{{ service.host_ip or service.ip }}</td>
                                    <td>{{ service.name or service.service }}</td>
                                    <td>{{ service.version or '未知' }}</td>
                                    <td>{{ service.port }}</td>
                                    <td>{{ service.protocol or 'tcp' }}</td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-message">未识别到任何服务</div>
            {% endif %}
        </div>

        <div class="section">
            <div class="section-header">
                <h2>漏洞检测结果</h2>
                <span class="count-badge">{{ results.vulnerabilities|length }}</span>
            </div>
            {% if results.vulnerabilities and results.vulnerabilities|length > 0 %}
                <div class="vulnerability-summary">
                    <div class="vuln-chart">
                        <div class="vuln-count high">
                            <span class="count">{{ results.vulnerabilities|selectattr('severity', 'equalto', 'high')|list|length }}</span>
                            <span class="label">高危</span>
                        </div>
                        <div class="vuln-count medium">
                            <span class="count">{{ results.vulnerabilities|selectattr('severity', 'equalto', 'medium')|list|length }}</span>
                            <span class="label">中危</span>
                        </div>
                        <div class="vuln-count low">
                            <span class="count">{{ results.vulnerabilities|selectattr('severity', 'equalto', 'low')|list|length }}</span>
                            <span class="label">低危</span>
                        </div>
                    </div>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>漏洞名称</th>
                            <th>严重程度</th>
                            <th>影响URL</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for vulnerability in results.vulnerabilities %}
                            <tr>
                                <td>{{ vulnerability.name }}</td>
                                <td>
                                    <span class="severity-badge severity-{{ vulnerability.severity }}">
                                        {{ vulnerability.severity or '未知' }}
                                    </span>
                                </td>
                                <td>{{ vulnerability.affected_url or results.scan_info.target }}</td>
                                <td>{{ vulnerability.description or '无描述' }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-message">未发现任何漏洞</div>
            {% endif %}
        </div>

        <div class="footer">
            <p>本报告由网络安全扫描系统自动生成 &copy; {{ results.scan_info.end_time[:4] }}</p>
        </div>
    </div>
</body>
</html>
