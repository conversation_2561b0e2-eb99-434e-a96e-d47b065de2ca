Metadata-Version: 2.1
Name: mysql-connector-python
Version: 8.2.0
Summary: MySQL driver written in Python
Home-page: http://dev.mysql.com/doc/connector-python/en/index.html
Download-URL: http://dev.mysql.com/downloads/connector/python/
Author: Oracle and/or its affiliates
Author-email: 
License: GNU GPLv2 (with FOSS License Exception)
Keywords: mysql db
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
License-File: LICENSE.txt
Requires-Dist: protobuf <=4.21.12,>=4.21.1
Provides-Extra: compression
Requires-Dist: lz4 <=4.3.2,>=2.1.6 ; extra == 'compression'
Requires-Dist: zstandard <=0.19.0,>=0.12.0 ; extra == 'compression'
Provides-Extra: dns-srv
Requires-Dist: dnspython <=2.3.0,>=1.16.0 ; extra == 'dns-srv'
Provides-Extra: gssapi
Requires-Dist: gssapi <=1.8.2,>=1.6.9 ; extra == 'gssapi'
Provides-Extra: opentelemetry
Requires-Dist: Deprecated >=1.2.6 ; extra == 'opentelemetry'
Requires-Dist: typing-extensions >=3.7.4 ; extra == 'opentelemetry'
Requires-Dist: zipp >=0.5 ; extra == 'opentelemetry'


MySQL driver written in Python which does not depend on MySQL C client
libraries and implements the DB API v2.0 specification (PEP-249).
