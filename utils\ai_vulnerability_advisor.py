"""
AI漏洞顾问模块
使用Deepseek AI进行服务版本漏洞识别和提供修复建议
"""
import os
import re
import json
import hashlib
import openai
from utils.logger import logger
from utils.config import Config

class AIVulnerabilityAdvisor:
    """AI漏洞顾问类"""

    def __init__(self):
        """初始化AI漏洞顾问"""
        # 获取AI配置
        ai_config = Config.get_ai_config()
        self.api_key = ai_config.get('api_key')
        self.model = ai_config.get('model', 'deepseek-chat')
        self.base_url = "https://api.deepseek.com"

        # 初始化缓存
        self.advice_cache = {}  # 用于缓存漏洞修复建议
        self.vulnerability_cache = {}  # 用于缓存服务漏洞识别结果

        # 初始化OpenAI客户端
        if self.api_key:
            openai.api_key = self.api_key
            openai.api_base = self.base_url
            logger.info("AI漏洞顾问初始化成功")
        else:
            logger.warning("未配置API密钥，AI漏洞顾问功能将不可用")

    def identify_service_vulnerabilities(self, service_name, service_version):
        """
        识别服务版本可能存在的漏洞
        :param service_name: 服务名称
        :param service_version: 服务版本
        :return: 漏洞列表，每个漏洞包含名称、描述、CVE编号和修复建议
        """
        if not self.api_key:
            logger.warning("未配置API密钥，无法识别服务漏洞")
            return []

        try:
            # 生成缓存键
            cache_key = f"{service_name.lower()}:{service_version.lower()}"

            # 检查缓存
            if cache_key in self.vulnerability_cache:
                logger.info(f"从缓存获取服务 {service_name} {service_version} 的漏洞信息")
                return self.vulnerability_cache[cache_key]

            logger.info(f"开始识别服务 {service_name} {service_version} 的漏洞")

            # 构建提示
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的网络安全专家，精通各种服务和软件的漏洞识别。
你的任务是根据提供的服务名称和版本，识别可能存在的安全漏洞，并提供详细信息。
请确保你的回答准确、全面，并包含最新的CVE信息。"""
                },
                {
                    "role": "user",
                    "content": f"""
请根据以下服务信息，识别可能存在的漏洞：

服务名称: {service_name}
服务版本: {service_version}

请以JSON格式返回结果，包含以下字段：
1. vulnerabilities: 漏洞列表，每个漏洞包含以下信息：
   - name: 漏洞名称（简洁明了）
   - description: 漏洞详细描述（包括影响范围和可能的攻击方式）
   - severity: 严重程度（high/medium/low）
   - cve: CVE编号（如有）
   - affected_versions: 受影响的版本范围
   - solution: 简要修复建议（将在后续步骤中获取详细修复方案）

请确保返回的是有效的JSON格式，不要包含任何其他文本。如果没有找到漏洞，请返回空的vulnerabilities列表。
请尽可能详细地提供信息，特别是CVE编号和受影响的版本范围。
"""
                }
            ]

            # 调用API
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=messages,
                stream=False,
                temperature=0.3  # 降低温度以获得更确定性的回答
            )

            # 提取回复内容
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content.strip()

                # 尝试解析JSON
                try:
                    # 提取JSON部分
                    json_match = re.search(r'```json\s*([\s\S]*?)\s*```', content)
                    if json_match:
                        json_str = json_match.group(1)
                    else:
                        json_str = content

                    # 解析JSON
                    result = json.loads(json_str)
                    vulnerabilities = result.get('vulnerabilities', [])

                    # 为每个漏洞添加服务信息
                    for vuln in vulnerabilities:
                        vuln['service_name'] = service_name
                        vuln['service_version'] = service_version

                        # 生成唯一ID，用于缓存
                        vuln_id = self._generate_vulnerability_id(vuln)
                        vuln['vuln_id'] = vuln_id

                    # 缓存结果
                    self.vulnerability_cache[cache_key] = vulnerabilities

                    logger.info(f"成功识别 {len(vulnerabilities)} 个漏洞")
                    return vulnerabilities
                except json.JSONDecodeError as e:
                    logger.error(f"解析AI响应JSON失败: {e}")
                    # 尝试手动解析
                    vulnerabilities = self._manually_parse_vulnerabilities(content)

                    # 为每个漏洞添加服务信息
                    for vuln in vulnerabilities:
                        vuln['service_name'] = service_name
                        vuln['service_version'] = service_version

                        # 生成唯一ID，用于缓存
                        vuln_id = self._generate_vulnerability_id(vuln)
                        vuln['vuln_id'] = vuln_id

                    # 缓存结果
                    self.vulnerability_cache[cache_key] = vulnerabilities

                    return vulnerabilities

            logger.warning("AI未返回有效的漏洞信息")
            return []

        except Exception as e:
            logger.error(f"识别服务漏洞时出错: {e}")
            return []

    def _generate_vulnerability_id(self, vulnerability):
        """
        为漏洞生成唯一ID
        :param vulnerability: 漏洞信息
        :return: 唯一ID
        """
        # 使用CVE编号作为主要标识
        if 'cve' in vulnerability and vulnerability['cve'] != 'N/A':
            return vulnerability['cve']

        # 如果没有CVE编号，使用漏洞名称和描述的哈希值
        key_str = f"{vulnerability.get('name', '')}:{vulnerability.get('description', '')}"
        return hashlib.md5(key_str.encode()).hexdigest()

    def _manually_parse_vulnerabilities(self, content):
        """
        手动解析AI响应中的漏洞信息
        :param content: AI响应内容
        :return: 漏洞列表
        """
        vulnerabilities = []

        try:
            # 尝试匹配CVE编号和漏洞名称
            cve_matches = re.finditer(r'(CVE-\d{4}-\d{4,})[:\s]*(.*?)(?=CVE-\d{4}-\d{4,}|$)', content, re.DOTALL)

            for match in cve_matches:
                cve = match.group(1).strip()
                description = match.group(2).strip()

                # 尝试提取严重程度
                severity = 'medium'  # 默认中等严重程度
                if re.search(r'严重|critical|high', description, re.IGNORECASE):
                    severity = 'high'
                elif re.search(r'低|low', description, re.IGNORECASE):
                    severity = 'low'

                # 尝试提取修复建议
                solution_match = re.search(r'修复[建议方法法案]+[:：](.*?)(?=CVE-|$)', description, re.DOTALL)
                solution = solution_match.group(1).strip() if solution_match else "更新到最新版本或应用官方补丁"

                # 提取漏洞名称
                name_match = re.search(r'^(.*?)(?=：|:|\n)', description)
                name = name_match.group(1).strip() if name_match else f"CVE漏洞 {cve}"

                vulnerabilities.append({
                    'name': name,
                    'description': description,
                    'severity': severity,
                    'cve': cve,
                    'solution': solution
                })

            # 如果没有匹配到CVE，尝试按段落解析
            if not vulnerabilities:
                paragraphs = re.split(r'\n\s*\n', content)
                for i, para in enumerate(paragraphs):
                    if len(para.strip()) > 10:  # 忽略太短的段落
                        vulnerabilities.append({
                            'name': f"潜在漏洞 #{i+1}",
                            'description': para.strip(),
                            'severity': 'medium',
                            'cve': 'N/A',
                            'solution': "更新到最新版本或应用官方补丁"
                        })

            return vulnerabilities

        except Exception as e:
            logger.error(f"手动解析漏洞信息失败: {e}")
            return []

    def get_vulnerability_fix_advice(self, vulnerabilities):
        """
        获取漏洞修复建议
        :param vulnerabilities: 漏洞列表
        :return: 包含详细修复建议的漏洞列表
        """
        if not self.api_key or not vulnerabilities:
            return vulnerabilities

        try:
            logger.info(f"开始获取 {len(vulnerabilities)} 个漏洞的修复建议")

            # 跟踪已处理的漏洞ID，避免重复处理相同的漏洞
            processed_vuln_ids = set()

            # 为每个漏洞添加详细的修复建议
            for vuln in vulnerabilities:
                # 获取漏洞ID，如果没有则生成一个
                vuln_id = vuln.get('vuln_id')
                if not vuln_id:
                    vuln_id = self._generate_vulnerability_id(vuln)
                    vuln['vuln_id'] = vuln_id

                # 如果已经处理过相同的漏洞，从缓存中获取修复建议
                if vuln_id in self.advice_cache:
                    logger.info(f"从缓存获取漏洞 {vuln.get('name')} 的修复建议")
                    vuln['detailed_solution'] = self.advice_cache[vuln_id]['detailed_solution']
                    vuln['ai_advice'] = self.advice_cache[vuln_id]['ai_advice']
                    continue

                # 如果已经处理过相同的漏洞ID，但不在缓存中（本次扫描中的重复漏洞）
                if vuln_id in processed_vuln_ids:
                    # 查找已处理的相同漏洞
                    for processed_vuln in vulnerabilities:
                        if processed_vuln.get('vuln_id') == vuln_id and 'detailed_solution' in processed_vuln:
                            vuln['detailed_solution'] = processed_vuln['detailed_solution']
                            vuln['ai_advice'] = processed_vuln.get('ai_advice', processed_vuln['detailed_solution'])
                            logger.info(f"复用本次扫描中的漏洞 {vuln.get('name')} 的修复建议")
                            break
                    continue

                # 如果已经有详细的修复建议，则跳过
                if vuln.get('detailed_solution') and len(vuln.get('detailed_solution', '')) > 100:
                    processed_vuln_ids.add(vuln_id)
                    continue

                # 获取操作系统和环境信息（如果有）
                os_info = vuln.get('os_info', '未知')
                service_name = vuln.get('service_name', vuln.get('service', ''))
                service_version = vuln.get('service_version', vuln.get('version', ''))

                # 构建提示
                system_prompt = """你是一个专业的网络安全专家和系统管理员，精通各种漏洞的修复方法。
你的任务是提供详细、具体且可操作的漏洞修复建议，包括具体的命令和步骤。
请确保你的建议是准确的、最新的，并且考虑到不同环境的特殊性。
你的回答应该结构清晰，便于用户按步骤执行。
请使用Markdown格式来组织你的回答，使用代码块来展示命令。"""

                user_prompt = f"""请为以下漏洞提供详细的修复建议方案：

漏洞名称: {vuln.get('name', 'N/A')}
漏洞描述: {vuln.get('description', 'N/A')}
CVE编号: {vuln.get('cve', 'N/A')}
严重程度: {vuln.get('severity', 'medium')}
服务: {service_name}
版本: {service_version}
环境信息: {os_info}

请按以下结构提供修复方案：

## 1. 漏洞概述
简要说明漏洞的风险和影响。

## 2. 紧急缓解措施
提供可以立即执行的临时缓解措施，包括具体命令。

## 3. 永久修复方案
提供彻底解决问题的步骤，包括：
- 详细的命令行指令（适用于Linux/Windows）
- 配置文件修改示例（如适用）
- 版本升级指南（如适用）

## 4. 验证修复
提供验证漏洞已被修复的方法和命令。

## 5. 最佳安全实践
针对此类漏洞的长期防护建议。

## 6. 参考资源
提供2-3个权威参考资源（如官方安全公告、CVE详情页等）。

请确保所有命令和步骤都是具体的、可直接执行的，并针对不同操作系统提供对应的解决方案。
"""

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]

                # 调用API
                response = openai.ChatCompletion.create(
                    model=self.model,
                    messages=messages,
                    stream=False,
                    temperature=0.3,  # 降低温度以获得更确定性的回答
                    max_tokens=2000   # 允许更长的回答
                )

                # 提取回复内容
                if response.choices and len(response.choices) > 0:
                    advice = response.choices[0].message.content.strip()

                    # 处理回答，确保格式一致
                    advice = self._format_fix_advice(advice, vuln)

                    # 保存详细修复建议
                    vuln['detailed_solution'] = advice

                    # 提取简短修复步骤作为ai_advice字段
                    short_advice = self._extract_short_advice(advice)
                    vuln['ai_advice'] = short_advice

                    # 缓存修复建议
                    self.advice_cache[vuln_id] = {
                        'detailed_solution': advice,
                        'ai_advice': short_advice
                    }

                    # 标记为已处理
                    processed_vuln_ids.add(vuln_id)

                    logger.info(f"成功获取漏洞 {vuln.get('name')} 的修复建议")

            return vulnerabilities

        except Exception as e:
            logger.error(f"获取漏洞修复建议时出错: {e}")
            return vulnerabilities

    def _format_fix_advice(self, advice, vuln):
        """
        格式化修复建议，确保格式一致
        :param advice: 原始修复建议
        :param vuln: 漏洞信息
        :return: 格式化后的修复建议
        """
        # 如果建议没有以漏洞名称开头，添加一个标题
        if not advice.startswith("# ") and not advice.startswith("## "):
            advice = f"# {vuln.get('name', '漏洞')}修复方案\n\n{advice}"

        # 确保有漏洞概述部分
        if "## 1. 漏洞概述" not in advice and "漏洞概述" not in advice:
            overview = f"\n## 1. 漏洞概述\n{vuln.get('description', '无详细描述')}\n"
            # 在第一个标题前插入概述
            first_header = re.search(r"## \d+\.", advice)
            if first_header:
                pos = first_header.start()
                advice = advice[:pos] + overview + advice[pos:]
            else:
                advice = advice + overview

        return advice

    def _extract_short_advice(self, detailed_advice):
        """
        从详细建议中提取简短的修复步骤
        :param detailed_advice: 详细修复建议
        :return: 简短修复步骤
        """
        # 尝试提取紧急缓解措施部分
        emergency_section = re.search(r"## 2\. 紧急缓解措施\s*(.*?)(?=##|\Z)", detailed_advice, re.DOTALL)
        if emergency_section:
            # 提取前300个字符作为简短解决方案
            solution = emergency_section.group(1).strip()
            # 提取命令行指令
            commands = re.findall(r"```(?:bash|sh|cmd|powershell)?\s*(.*?)```", solution, re.DOTALL)
            if commands:
                return f"紧急缓解措施: {solution[:300]}..."
            else:
                return solution[:300] + "..."

        # 如果没有找到紧急缓解措施，尝试提取永久修复方案
        permanent_section = re.search(r"## 3\. 永久修复方案\s*(.*?)(?=##|\Z)", detailed_advice, re.DOTALL)
        if permanent_section:
            solution = permanent_section.group(1).strip()
            return solution[:300] + "..."

        # 如果都没有找到，返回前300个字符
        return detailed_advice[:300] + "..."
