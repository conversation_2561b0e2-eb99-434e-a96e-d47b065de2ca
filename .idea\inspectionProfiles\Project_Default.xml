<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="pyinstaller" />
            <item index="1" class="java.lang.String" itemvalue="pywin32" />
            <item index="2" class="java.lang.String" itemvalue="opencv-python" />
            <item index="3" class="java.lang.String" itemvalue="PyQt5" />
            <item index="4" class="java.lang.String" itemvalue="pyqt5-plugins" />
            <item index="5" class="java.lang.String" itemvalue="pywin32-ctypes" />
            <item index="6" class="java.lang.String" itemvalue="PyQt5-sip" />
            <item index="7" class="java.lang.String" itemvalue="pyqt5-tools" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="pyinstaller-hooks-contrib" />
            <item index="10" class="java.lang.String" itemvalue="PyQtWebEngine" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>